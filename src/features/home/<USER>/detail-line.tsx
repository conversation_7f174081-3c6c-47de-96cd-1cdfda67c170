import ReactECharts from 'echarts-for-react'
import { useAtomValue } from 'jotai'
import { useMemo } from 'react'

import { getCarbonInvestmentQueryAtom } from '../atoms'

const option = {
  title: {
    left: 'center',
    top: 10,
    textStyle: {
      fontSize: 16,
      fontWeight: 'bold',
    },
    subtext: '2015-2025',
    subtextStyle: {
      fontSize: 12,
    },
  },
  tooltip: {
    trigger: 'axis',
    valueFormatter: (value: number) => `${value.toFixed(2)}%`,
  },
  legend: {
    data: ['化石燃料相关', '高碳'],
    top: 40,
    right: 10,
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '18%',
    containLabel: true,
  },
  dataZoom: [
    {
      type: 'slider',
      show: true,
      xAxisIndex: 0,
      start: 0,
      end: 40,
      minValueSpan: 4,
      maxValueSpan: 4,
      brushSelect: false,
      height: 8,
      backgroundColor: '#f5f5f5',
      fillerColor: '#222',
      borderColor: 'rgba(0,0,0,0)',
      handleIcon:
        'path://M512 512m-32 0a32 32 0 1 0 64 0a32 32 0 1 0 -64 0',
      handleSize: 12,
      handleStyle: {
        color: '#fff',
        borderColor: '#222',
        borderWidth: 6,
        shadowBlur: 0,
      },
      moveHandleStyle: {
        color: '#222',
        borderColor: '#222',
        borderWidth: 6,
      },
      showDetail: false,
    },
  ],
  xAxis: {
    type: 'category',
    boundaryGap: false,
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}%',
    },
  },
}

export function DetailLine() {
  const { data: list } = useAtomValue(getCarbonInvestmentQueryAtom)

  const years = useMemo(() => {
    return list?.allYears
  }, [list])

  const echartOption = useMemo(() => {
    return {
      ...option,
      title: {
        ...option.title,
        text: `中化石燃料相关行业以及高碳类型行业的投资公司投资占比`,
      },
      xAxis: {
        ...option.xAxis,
        data: years || [],
      },
      series: [
        {
          name: '化石燃料相关',
          type: 'line',
          data: list?.ffProportionInYearList,
        },
        {
          name: '高碳',
          type: 'line',
          data: list?.gtProportionInYearList,
        },
      ],

    }
  }, [years, list])

  return (
    <div className="pt-4 bg-white container">
      <ReactECharts option={echartOption} style={{ height: 1068 }} />
    </div>
  )
}
