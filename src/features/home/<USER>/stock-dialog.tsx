import type { ColumnDef } from '@tanstack/react-table'
import { flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table'
import { useAtom, useAtomValue } from 'jotai'
import { ArrowDownToLineIcon, ArrowLeftIcon } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog'
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

import type { DownloadStocksListParams, DownLoadStocksListResult } from '../atoms'
import { downloadStocksList, fundIdAtom, getStocksByMarkQueryAtom, mark<PERSON>tom, selectedYear<PERSON>tom, stockPaginationAtom } from '../atoms'
import { useDownload } from '../hooks/useDownload'

type StockData = {
  stockName: string
  stockCode: string
  rptDate: string
  proportiontototalstockinvestments: number
  windIndustry: string | null
  gpType: string | null
  FFMark: string | null
  GTMark: string | null
}

export type StockDialogProps = {
  isOpen: boolean
  setIsOpen: (value: boolean) => void
  title?: string
}

export function StockDialog({ isOpen, setIsOpen, title }: StockDialogProps) {
  const { data } = useAtomValue(getStocksByMarkQueryAtom)
  const [pagination, setPagination] = useAtom(stockPaginationAtom)
  // ${已选择的基金名称}${已选择的报告时间}${已选择的公司分类}公司列表
  const fileName = `公司列表.xlsx`
  const { isPending, download } = useDownload<DownloadStocksListParams, DownLoadStocksListResult>(downloadStocksList, fileName)

  const [windId] = useAtom(fundIdAtom)
  const [year] = useAtom(selectedYearAtom)
  const [mark] = useAtom(markAtom)

  const columns: Array<ColumnDef<StockData>> = [
    {
      accessorKey: 'stockName',
      header: '股票名称',
    },
    {
      accessorKey: 'stockCode',
      header: '股票代码',
    },
    {
      accessorKey: 'rptDate',
      header: '报告时间',
    },
    {
      accessorKey: 'proportiontototalstockinvestments',
      header: '投资占比',
      cell: ({ row }) => `${(row.getValue('proportiontototalstockinvestments') as number).toFixed(2)}%`,
    },
    {
      accessorKey: 'windIndustry',
      header: 'WIND 行业名称',
    },
    {
      accessorKey: 'gpType',
      header: '本数据库行业分类',
    },
    {
      accessorKey: 'FFMark',
      header: '是否所属化石燃料相关行业',
    },
    {
      accessorKey: 'GTMark',
      header: '是否所属高碳类型行业',
    },
  ]

  const table = useReactTable({
    data: (data?.list || []) as StockData[],
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    pageCount: Math.ceil((data?.pagination?.total || 0) / pagination.size),
    state: {
      pagination: {
        pageIndex: pagination.page - 1,
        pageSize: pagination.size,
      },
    },
  })

  const handleDownload = () => {
    download({
      windId: windId!,
      year: year!,
      mark: mark!,
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-screen h-screen rounded-none !flex !flex-col gap-3.5" showCloseButton={false}>
        <div className="flex justify-between items-center">
          <Button variant="ghost" className="flex items-center gap-2" onClick={() => setIsOpen(false)}>
            <ArrowLeftIcon className="w-6 h-6" />
            <span>返回</span>
          </Button>
          <DialogTitle className="text-[28px] font-bold">{title}</DialogTitle>
          <Button
            size="lg"
            onClick={handleDownload}
            disabled={isPending}
            className="bg-[#001e3d] text-white font-normal tracking-normal hover:bg-[#002a4d] disabled:opacity-50 rounded-none"
          >
            <ArrowDownToLineIcon className="w-6 h-6" />
            <span className="">{isPending ? '下载中...' : '下载全部'}</span>
          </Button>
        </div>
        <div className="flex flex-col gap-4 py-4 h-full overflow-hidden">
          <div className="overflow-auto flex-1">
            <Table>
              <TableHeader className="bg-[#001e3d]">
                {table.getHeaderGroups().map(headerGroup => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <TableHead key={header.id} className="bg-[#001e3d] text-white border-b border-r border-white text-center p-5 text-2xl last:border-r-0">
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row, index) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && 'selected'}
                      className={`${
                        index % 2 === 0 ? 'bg-[rgba(240,240,240,1)]' : 'bg-[rgba(245,245,245,1)]'
                      }`}
                    >
                      {row.getVisibleCells().map(cell => (
                        <TableCell key={cell.id} className="text-center p-5 text-wrap whitespace-normal text-[22px] border-r border-white last:border-r-0">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      暂无数据
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
            <Pagination className="flex items-center justify-end space-x-2 mt-10">
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious className={pagination.page <= 1 ? 'opacity-50' : ''} onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))} />
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink isActive={pagination.page === 1} onClick={() => setPagination(prev => ({ ...prev, page: 1 }))}>1</PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationEllipsis />
                </PaginationItem>
                <PaginationItem>
                  <PaginationNext className={pagination.page >= Math.ceil(data?.pagination.total || 0) / pagination.size ? 'opacity-50' : ''} onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))} />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
