import type { ColumnDef } from '@tanstack/react-table'
import { flexRender, getCoreRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table'
import { useAtom, useAtomValue } from 'jotai'
import { ArrowDownToLineIcon } from 'lucide-react'
import { useMemo } from 'react'

import { FFBlackIcon, FFWhiteIcon } from '@/components/icons/ff-icon'
import { GtBlackIcon, GtWhiteIcon } from '@/components/icons/gt-icon'
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

import type { DownloadTopHoldingsParams, DownLoadTopHoldingsResult, TopHoldingsInfo } from '../atoms'
import { downloadTopHoldings, fundIdAtom, getTopHoldingsQueryAtom, selectedYearAtom } from '../atoms'
import { useDownload } from '../hooks/useDownload'

export function DetailTopHoldings() {
  const { data = [] } = useAtomValue(getTopHoldingsQueryAtom)
  const [windId] = useAtom(fundIdAtom)
  const [year, _setYear] = useAtom(selectedYearAtom)

  const columns: Array<ColumnDef<TopHoldingsInfo>> = useMemo(() => [
    {
      accessorKey: 'stockName',
      header: '股票名称',
    },
    {
      accessorKey: 'stockCode',
      header: '股票代码',
    },
    {
      accessorKey: 'proportiontototalstockinvestments',
      header: '投资占比%',
      cell: ({ row }) => `${(row.getValue('proportiontototalstockinvestments') as number).toFixed(2)}`,
    },
    {
      accessorKey: 'FFMark',
      header: () => (
        <div className="flex justify-center items-center gap-3.5">
          <span className="text-2xl">是否化石</span>
          <FFWhiteIcon className="size-6" />
        </div>
      ),
      cell: ({ row }) => {
        const value = row.getValue('FFMark') as string
        return value === '化石燃料相关'
          ? (
              <div className="flex justify-center items-center">
                <FFBlackIcon className="size-8" />
              </div>
            )
          : null
      },
    },
    {
      accessorKey: 'GTMark',
      header: () => (
        <div className="flex justify-center items-center gap-3.5">
          <span className="text-2xl">是否高碳</span>
          <GtWhiteIcon className="size-6" />
        </div>
      ),
      cell: ({ row }) => {
        const value = row.getValue('GTMark') as string
        return value === '高碳'
          ? (
              <div className="flex justify-center items-center">
                <GtBlackIcon className="size-8" />
              </div>
            )
          : null
      },
    },
  ], [])

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  })

  // YYYY-MM-DD ${已选择的基金名称} ${已选择的报告时间} 持仓信息
  const fileName = `${new Date().getFullYear()}-${new Date().getMonth() + 1}-${new Date().getDate()} ${windId} ${year} 持仓信息.xlsx`
  const { isPending, download } = useDownload<DownloadTopHoldingsParams, DownLoadTopHoldingsResult>(downloadTopHoldings, fileName)

  async function handleDownload() {
    await download({
      windId: windId!,
      year,
    })
  }

  return (
    <div className="mt-16 container">
      <div className="flex justify-center items-center mb-6">
        <span className="text-[34px]">持仓前十公司基本信息</span>
      </div>
      <Table>
        <TableHeader className="bg-[#001e3d]">
          {table.getHeaderGroups().map(headerGroup => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map(header => (
                <TableHead key={header.id} className="bg-[#001e3d] text-white border-b border-r border-white text-center p-5 text-2xl last:border-r-0">
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows.map((row, index) => (
            <TableRow
              key={row.id}
              className={`last:!border-b-2 last:!border-[rgba(51,51,51,1)] ${
                index % 2 === 0 ? 'bg-[rgba(240,240,240,1)]' : 'bg-[rgba(245,245,245,1)]'
              }`}
            >
              {row.getVisibleCells().map((cell) => {
                const cellDef = cell.column.columnDef.cell
                return (
                  <TableCell key={cell.id} className="text-center p-5 text-wrap whitespace-normal text-[22px] border-r border-white last:border-r-0">
                    {flexRender(cellDef, cell.getContext())}
                  </TableCell>
                )
              })}
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <div className="flex justify-end items-center mt-10">
        <Button
          size="lg"
          onClick={handleDownload}
          disabled={isPending}
          className="bg-[#001e3d] text-white font-normal tracking-normal hover:bg-[#002a4d] disabled:opacity-50 rounded-none"
        >
          <ArrowDownToLineIcon className="w-6 h-6" />
          <span className="">{isPending ? '下载中...' : '下载数据'}</span>
        </Button>
      </div>
    </div>
  )
}
