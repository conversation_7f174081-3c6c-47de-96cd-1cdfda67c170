import ReactECharts from 'echarts-for-react'
import { useAtom, useAtomValue } from 'jotai'
import { useMemo, useState } from 'react'

import { Calendar7Icon } from '@/components/icons/calendar-7-icon'
import { SelectWithIcon } from '@/components/select-with-icon'

import { getCompanyTypeStatisticsQuery<PERSON>tom, getYearsQueryAtom, markAtom, selectedYearAtom, stockPaginationAtom } from '../atoms'
import { usePieOption } from '../hooks/usePieOption'
import { StockDialog } from './stock-dialog'

export function DetailPie() {
  const { data } = useAtomValue(getCompanyTypeStatisticsQueryAtom)
  const { data: yearList } = useAtomValue(getYearsQueryAtom)
  const [year, setYear] = useAtom(selectedYearAtom)

  const [, setMark] = useAtom(markAtom)
  const [, setPagination] = useAtom(stockPaginationAtom)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedData, setSelectedData] = useState<{
    title: string
    name: string
    value: number
    percent: number
  } | null>(null)

  useMemo(() => {
    if (yearList && yearList.length > 0 && !year) {
      setYear(yearList[0])
    }
  }, [yearList, year, setYear])

  const { ffOption, gtOption } = usePieOption({ data })

  const handleYearChange = (value: string) => {
    setYear(value)
  }

  const handleFFChartClick = (params: any) => {
    setMark(params.data.mark)
    setPagination(prev => ({ ...prev, page: 1 }))
    setSelectedData({
      title: '化石燃料相关行业投资公司数量占比',
      name: params.name,
      value: params.value,
      percent: params.percent,
    })
    setIsDialogOpen(true)
  }

  const handleGTChartClick = (params: any) => {
    setMark(params.data.mark)
    setPagination(prev => ({ ...prev, page: 1 }))
    setSelectedData({
      title: '高碳类型行业公司数量占比',
      name: params.name,
      value: params.value,
      percent: params.percent,
    })
    setIsDialogOpen(true)
  }

  return (
    <div className="flex flex-col gap-7 mt-14 container">
      <div className="flex justify-end">
        <SelectWithIcon
          defaultValue={year}
          onValueChange={handleYearChange}
          data={yearList?.map(year => ({ label: year, value: year })) || []}
          icon={<Calendar7Icon className="size-5 mr-3 flex-shrink-0" />}
        />
      </div>
      <div className="h-[350px] pt-4 bg-white rounded shadow">
        {year && (
          <div className="flex flex-row gap-4">
            <ReactECharts
              option={ffOption}
              style={{ height: 320, width: '50%' }}
              onEvents={{
                click: handleFFChartClick,
              }}
            />
            <ReactECharts
              option={gtOption}
              style={{ height: 320, width: '50%' }}
              onEvents={{
                click: handleGTChartClick,
              }}
            />
          </div>
        )}
      </div>
      {isDialogOpen && <StockDialog isOpen={isDialogOpen} setIsOpen={setIsDialogOpen} title={selectedData?.title} />}
    </div>
  )
}
