import { PageFooter } from '@/components/page-footer'

import { DetailBanner } from './components/detail-banner'
import { DetailLine } from './components/detail-line'
import { DetailPie } from './components/detail-pie'
import { DetailTopHoldings } from './components/detail-top-holdings'

export function FundDetail() {
  return (
    <div className="flex min-h-screen flex-col bg-[#f5f5f5]">
      <header className="flex h-16 relative bg-[#081d3b]">
        <div className="flex items-center container">
          <div className="flex items-center">
            <a rel="noreferrer noopener" href="https://www.greenpeace.org.cn/" target="_blank">
              <img alt="Greenpeace" className="my-4 h-6 pr-4" src="https://green-finance-monitor.greenpeace.org.cn/assets/greenpeace__logo--green-CTS8Zpoe.svg" />
            </a>
          </div>
        </div>
      </header>
      <DetailBanner />
      <DetailLine />
      <DetailPie />
      <DetailTopHoldings />
      <PageFooter />
    </div>
  )
}
