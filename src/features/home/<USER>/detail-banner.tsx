import { Link } from '@tanstack/react-router'
import { useAtom, useAtomValue } from 'jotai'
import { ArrowLeftIcon } from 'lucide-react'
import { useMemo } from 'react'

import { SelectIcon } from '@/components/icons/select-icon'
import { SelectWithIcon } from '@/components/select-with-icon'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'

import { fundIdAtom, getFundNamesQueryAtom } from './../atoms'

export function DetailBanner() {
  const id = useAtomValue(fundIdAtom)
  const { data, isLoading } = useAtomValue(getFundNamesQueryAtom)
  const [, setFundIdAtom] = useAtom(fundIdAtom)

  const defaultValue = useMemo(() => {
    return id || (data?.fundNames?.[0]?.value || '')
  }, [id, data])

  return (
    <div className="flex justify-between container pt-14 pb-7">
      <Button variant="ghost" asChild className="flex items-center gap-2">
        <Link to="/">
          <ArrowLeftIcon className="w-6 h-6" />
          <span>返回</span>
        </Link>
      </Button>
      <div>
        {isLoading ? (
          <Skeleton className="h-10 w-24" />
        ) : (
          <SelectWithIcon
            defaultValue={defaultValue}
            onValueChange={setFundIdAtom}
            data={data?.fundNames?.map(item => ({ label: item.label, value: item.value })) || []}
            icon={<SelectIcon className="w-4 h-4 mr-3 flex-shrink-0" />}
          />
        )}
      </div>
    </div>
  )
}
