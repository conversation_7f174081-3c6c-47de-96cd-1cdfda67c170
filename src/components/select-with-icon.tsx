import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'

interface SelectWithIconProps {
  defaultValue: string
  onValueChange: (value: string) => void
  data: Array<{
    label: string
    value: string
  }>
  icon: React.ReactNode
}

export function SelectWithIcon({ defaultValue, onValueChange, data, icon }: SelectWithIconProps) {
  return (
    <Select defaultValue={defaultValue} onValueChange={onValueChange}>
      <SelectTrigger
        className="w-[614px] !h-14 bg-white overflow-hidden p-0 relative
                         before:content-[''] before:absolute before:right-0 before:top-0 before:w-14 before:h-full before:bg-[#081d3b] before:z-0
                         [&>svg]:absolute [&>svg]:right-4 [&>svg]:top-1/2 [&>svg]:-translate-y-1/2 [&>svg]:z-10 [&>svg]:text-white [&>svg]:opacity-100 text-xl"
        iconClassName="text-white opacity-100 w-5 h-5 z-10"
      >
        <div className="flex items-center flex-1 pl-4 pr-14 relative z-10">
          {icon}
          <SelectValue placeholder="基金名称" className="text-gray-800 font-medium !text-xl flex-1 text-left" />
        </div>
      </SelectTrigger>
      <SelectContent className="border border-gray-200 shadow-lg">
        {data?.map(({ label, value }) => (
          <SelectItem key={value} value={value} className="text-xl py-2 px-4 hover:bg-gray-50">
            {label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}
